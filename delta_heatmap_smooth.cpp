#include "sierrachart.h"

SCDLLName("Delta Heatmap Smooth DLL")

/*==========================================================================*/
SCSFExport scsf_DeltaHeatmapSmooth(SCStudyInterfaceRef sc)
{
    // Input references
    SCInputRef SmoothingPeriod = sc.Input[0];
    SCInputRef GradientScale = sc.Input[1];
    SCInputRef StrongBuyColor = sc.Input[2];
    SCInputRef StrongSellColor = sc.Input[3];
    SCInputRef NeutralColor = sc.Input[4];
    SCInputRef ExtremeThreshold = sc.Input[5];
    SCInputRef AggressorThreshold = sc.Input[6];
    SCInputRef ShowAggressorSignals = sc.Input[7];
    SCInputRef AggressorBuyColor = sc.Input[8];
    SCInputRef AggressorSellColor = sc.Input[9];

    // Subgraph references
    SCSubgraphRef SmoothedDelta = sc.Subgraph[0];
    SCSubgraphRef DeltaHeatmap = sc.Subgraph[1];  // Must be subgraph 1 for gradient coloring
    SCSubgraphRef AvgAbsDelta = sc.Subgraph[2];
    SCSubgraphRef ExtremeBuyCircles = sc.Subgraph[3];
    SCSubgraphRef ExtremeSellCircles = sc.Subgraph[4];
    SCSubgraphRef AggressorBuyArrows = sc.Subgraph[5];
    SCSubgraphRef AggressorSellArrows = sc.Subgraph[6];
    
    SCFloatArrayRef AbsDelta = AvgAbsDelta.Arrays[0];
    SCFloatArrayRef Delta = AvgAbsDelta.Arrays[1];


    if (sc.SetDefaults)
    {
        // Set the configuration and defaults
        sc.GraphName = "Delta Heatmap Smooth";
        sc.StudyDescription = "Heatmap overlay that colors candle bodies, borders and wicks based on smoothed delta values (Buy vs Sell control). Note: Chart must be set to Candlestick mode to display as candlesticks instead of OHLC bars.";
        
        sc.GraphRegion = 0; // Main price graph
        sc.AutoLoop = 1;
        
        // Input parameters
        SmoothingPeriod.Name = "Lookback Smoothing (bars)";
        SmoothingPeriod.SetInt(20);
        SmoothingPeriod.SetIntLimits(1, 500);
        
        GradientScale.Name = "Gradient Scale (× Avg|Δ|)";
        GradientScale.SetFloat(1.0f);
        GradientScale.SetFloatLimits(0.1f, 10.0f);
        
        StrongBuyColor.Name = "Strong Buy Color";
        StrongBuyColor.SetColor(RGB(0, 128, 255)); // Blue
        
        StrongSellColor.Name = "Strong Sell Color";
        StrongSellColor.SetColor(RGB(255, 0, 0)); // Red
        
        NeutralColor.Name = "Neutral Color";
        NeutralColor.SetColor(RGB(192, 192, 192)); // Gray

        ExtremeThreshold.Name = "Extreme Color Threshold (0.8-1.0)";
        ExtremeThreshold.SetFloat(0.85f);
        ExtremeThreshold.SetFloatLimits(0.8f, 1.0f);

        AggressorThreshold.Name = "Big Aggressor Delta Threshold";
        AggressorThreshold.SetFloat(50.0f);
        AggressorThreshold.SetFloatLimits(1.0f, 1000.0f);

        ShowAggressorSignals.Name = "Show Big Aggressor Signals";
        ShowAggressorSignals.SetYesNo(1);

        AggressorBuyColor.Name = "Aggressor Buy Signal Color";
        AggressorBuyColor.SetColor(RGB(0, 255, 0)); // Bright Green

        AggressorSellColor.Name = "Aggressor Sell Signal Color";
        AggressorSellColor.SetColor(RGB(255, 0, 255)); // Magenta

        // Configure subgraphs
        SmoothedDelta.Name = "Smoothed Delta";
        SmoothedDelta.DrawStyle = DRAWSTYLE_IGNORE;

        // Configure Delta Heatmap as subgraph 1 (required for gradient coloring)
        DeltaHeatmap.Name = "Delta Heatmap";
        DeltaHeatmap.DrawStyle = DRAWSTYLE_COLOR_BAR;
        DeltaHeatmap.PrimaryColor = RGB(192, 192, 192);
        DeltaHeatmap.SecondaryColor = RGB(192, 192, 192);
        DeltaHeatmap.DrawZeros = 1;
        DeltaHeatmap.LineWidth = 1;

        AvgAbsDelta.Name = "Average Absolute Delta";
        AvgAbsDelta.DrawStyle = DRAWSTYLE_IGNORE;

        // Configure extreme buy circles
        ExtremeBuyCircles.Name = "Extreme Buy Circles";
        ExtremeBuyCircles.DrawStyle = DRAWSTYLE_TRANSPARENT_CIRCLE;
        ExtremeBuyCircles.PrimaryColor = StrongBuyColor.GetColor();
        ExtremeBuyCircles.LineWidth = 10;
        ExtremeBuyCircles.DrawZeros = 0;

        // Configure extreme sell circles
        ExtremeSellCircles.Name = "Extreme Sell Circles";
        ExtremeSellCircles.DrawStyle = DRAWSTYLE_TRANSPARENT_CIRCLE;
        ExtremeSellCircles.PrimaryColor = StrongSellColor.GetColor();
        ExtremeSellCircles.LineWidth = 10;
        ExtremeSellCircles.DrawZeros = 0;

        // Configure aggressor buy arrows
        AggressorBuyArrows.Name = "Big Aggressor Buy Signals";
        AggressorBuyArrows.DrawStyle = DRAWSTYLE_ARROW_UP;
        AggressorBuyArrows.PrimaryColor = AggressorBuyColor.GetColor();
        AggressorBuyArrows.LineWidth = 3;
        AggressorBuyArrows.DrawZeros = 0;

        // Configure aggressor sell arrows
        AggressorSellArrows.Name = "Big Aggressor Sell Signals";
        AggressorSellArrows.DrawStyle = DRAWSTYLE_ARROW_DOWN;
        AggressorSellArrows.PrimaryColor = AggressorSellColor.GetColor();
        AggressorSellArrows.LineWidth = 3;
        AggressorSellArrows.DrawZeros = 0;
        
        return;
    }
    
    int period = SmoothingPeriod.GetInt();
    
	Delta[sc.Index] = sc.AskVolume[sc.Index] - sc.BidVolume[sc.Index];
	AbsDelta[sc.Index] = fabs(Delta[sc.Index]);

	sc.WeightedMovingAverage(Delta, SmoothedDelta, period);
	sc.MovingAverage(AbsDelta, AvgAbsDelta, MOVAVGTYPE_SIMPLE, period);
    
    // Map delta to color
    float smoothedDelta = SmoothedDelta[sc.Index];
    float avgAbsDelta = AvgAbsDelta[sc.Index];
    float scale = GradientScale.GetFloat();

    COLORREF barColor = NeutralColor.GetColor();
    float normalizedDelta = 0.0f;

    if (avgAbsDelta > 0 && scale > 0)
    {
        normalizedDelta = smoothedDelta / (avgAbsDelta * scale);

        // Clamp to [-1, 1]
        if (normalizedDelta > 1.0f)
            normalizedDelta = 1.0f;

        if (normalizedDelta < -1.0f)
            normalizedDelta = -1.0f;

        if (normalizedDelta >= 0)
			barColor = sc.RGBInterpolate(NeutralColor.GetColor(), StrongBuyColor.GetColor(), normalizedDelta);
        else
            barColor = sc.RGBInterpolate(NeutralColor.GetColor(), StrongSellColor.GetColor(), -normalizedDelta);
    }

    // Apply color to the bar
    sc.DataStartIndex = period - 1; // Don't color bars until we have enough data

    if (sc.Index >= sc.DataStartIndex)
    {
        DeltaHeatmap[sc.Index] = sc.Close[sc.Index];
        DeltaHeatmap.DataColor[sc.Index] = barColor;

        // Check for extreme colors and place circles
        float extremeThreshold = ExtremeThreshold.GetFloat();

        // Clear previous circle and arrow values
        ExtremeBuyCircles[sc.Index] = 0.0f;
        ExtremeSellCircles[sc.Index] = 0.0f;
        AggressorBuyArrows[sc.Index] = 0.0f;
        AggressorSellArrows[sc.Index] = 0.0f;

        // Check if this bar has extreme buy color (normalized delta >= threshold)
        if (normalizedDelta >= extremeThreshold)
        {
            // Place blue circle at the high of the candle
            ExtremeBuyCircles[sc.Index] = sc.High[sc.Index];
        }
        // Check if this bar has extreme sell color (normalized delta <= -threshold)
        else if (normalizedDelta <= -extremeThreshold)
        {
            // Place magenta circle at the low of the candle
            ExtremeSellCircles[sc.Index] = sc.Low[sc.Index];
        }
    }
}
