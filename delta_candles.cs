using System;
using System.Collections.Generic;
using System.Drawing;
using System.Reflection;
using TradingPlatform.BusinessLayer;

namespace DeltaHeatmapSmooth
{
    // Delta Heatmap Smooth Overlay - usa SetBarAppearance quando disponibile
    public class DeltaHeatmapSmooth : Indicator, IVolumeAnalysisIndicator
    {
        [InputParameter("Lookback smoothing (bars)", 0, 1, 500, 1, 0)]
        public int Smoothing = 20;

        [InputParameter("Gradient scale (× Avg|Δ|)", 10, 0.1, 10.0, 0.1, 1)]
        public double GradientScale = 2.0;

        [InputParameter("Strong Buy Color", 20)]
        public Color StrongBuyColor = Color.FromArgb(0, 179, 255);

        [InputParameter("Strong Sell Color", 30)]
        public Color StrongSellColor = Color.FromArgb(255, 0, 213);

        [InputParameter("Neutral Color", 40)]
        public Color NeutralColor = Color.Gray;

        // Richiesta Volume Analysis
        public bool IsRequirePriceLevelsCalculation => false;

        // candidate property names per IndicatorBarAppearance (varia a seconda versione API)
        private readonly string[] _appearanceCandidates = new[]
        {
            "BackgroundColor", "BodyColor", "FillColor",
            "BorderColor", "OutlineColor",
            "WickColor", "ShadowColor"
        };

        public DeltaHeatmapSmooth()
        {
            Name = "Delta Heatmap Smooth (overlay)";
            Description = "Heatmap overlay che colora corpo, bordi e ombre delle candele in base al controllo (Buy vs Sell).";
            SeparateWindow = false;
        }

        public override string ShortName => "Δ Heatmap Smooth";

        public void VolumeAnalysisData_Loaded()
        {
            // ricalcolo storico (applica colori a tutte le barre)
            for (int i = 0; i < Count; i++)
                ApplySmoothColor(i);
        }

        protected override void OnUpdate(UpdateArgs args)
        {
            ApplySmoothColor(0);
        }

        // ========= Logica principale =========
        private void ApplySmoothColor(int offset)
        {
            // smoothed delta usando weighted moving average (più peso alle barre recenti)
            double smoothedDelta = ComputeWeightedSmoothedDelta(offset, Smoothing);
            double avgAbsDelta = ComputeAvgAbsDelta(offset, Smoothing);

            var color = MapDeltaToColor(smoothedDelta, avgAbsDelta, GradientScale);

            // Prima prova: ottieni l'appearance correntemente usata e prova a impostare border/wick/body
            try
            {
                var barAppearance = GetBarAppearance(offset); // returns IndicatorBarAppearance (API)
                if (barAppearance != null)
                {
                    ApplyColorToAppearance(barAppearance, color);
                    // Imposta la nuova appearance sulla barra
                    SetBarAppearance(barAppearance, offset);
                    return;
                }
            }
            catch
            {
                // se qualcosa non va con SetBarAppearance -> fallback
            }

            // Fallback: colora almeno il corpo della barra
            SetBarColor(color, offset);
        }

        // prova a impostare le proprietà colore su IndicatorBarAppearance usando reflection
        private void ApplyColorToAppearance(object barAppearanceObj, Color color)
        {
            if (barAppearanceObj == null) return;
            var t = barAppearanceObj.GetType();

            foreach (var propName in _appearanceCandidates)
            {
                var p = t.GetProperty(propName, BindingFlags.Public | BindingFlags.Instance);
                if (p == null || !p.CanWrite) continue;

                // se il tipo della proprietà è Color o Nullable<Color>
                if (p.PropertyType == typeof(Color) || p.PropertyType == typeof(Color?))
                {
                    p.SetValue(barAppearanceObj, color);
                    continue;
                }

                // se la proprietà è un oggetto (es. SettingItemColor) cerchiamo una sua sub-prop "Color"
                var nestedColorProp = p.PropertyType.GetProperty("Color", BindingFlags.Public | BindingFlags.Instance);
                if (nestedColorProp != null && nestedColorProp.CanWrite &&
                    (nestedColorProp.PropertyType == typeof(Color) || nestedColorProp.PropertyType == typeof(Color?)))
                {
                    var nestedObj = p.GetValue(barAppearanceObj) ?? Activator.CreateInstance(p.PropertyType);
                    nestedColorProp.SetValue(nestedObj, color);
                    p.SetValue(barAppearanceObj, nestedObj);
                    continue;
                }

                // alternativa: property di tipo string contenente codice colore (es. "#RRGGBB") - non comune ma tentiamo
                if (p.PropertyType == typeof(string))
                {
                    string hex = $"#{color.R:X2}{color.G:X2}{color.B:X2}";
                    p.SetValue(barAppearanceObj, hex);
                    continue;
                }
            }
        }

        // ========= Smoothing e utility =========

        // Weighted moving average (più peso alle barre vicine, per una sfumatura più fluida)
        private double ComputeWeightedSmoothedDelta(int offset, int period)
        {
            if (period <= 1) period = 1;
            int end = Math.Min(this.Count, offset + period);
            double sum = 0.0;
            double wSum = 0.0;
            int idx = 0;
            for (int k = offset; k < end; k++)
            {
                var va = GetVolumeAnalysisData(k);
                if (va?.Total == null) continue;
                // weight: period - idx (recent bars have big weight)
                double weight = period - idx;
                sum += va.Total.Delta * weight;
                wSum += weight;
                idx++;
            }
            return wSum > 0 ? sum / wSum : 0.0;
        }

        private double ComputeAvgAbsDelta(int offset, int period)
        {
            if (period <= 1) period = 1;
            int end = Math.Min(this.Count, offset + period);
            double sum = 0.0; int n = 0;
            for (int k = offset; k < end; k++)
            {
                var va = GetVolumeAnalysisData(k);
                if (va?.Total == null) continue;
                sum += Math.Abs(va.Total.Delta);
                n++;
            }
            return n > 0 ? sum / n : 1.0;
        }

        // ========= Colore Heatmap Smooth =========
        private Color MapDeltaToColor(double delta, double avgAbsDelta, double scale)
        {
            if (avgAbsDelta <= 0 || scale <= 0) return NeutralColor;

            double x = delta / (avgAbsDelta * scale);
            if (x > 1) x = 1;
            if (x < -1) x = -1;

            if (x >= 0)
                return LerpColor(NeutralColor, StrongBuyColor, x);
            else
                return LerpColor(NeutralColor, StrongSellColor, -x);
        }

        private Color LerpColor(Color c1, Color c2, double t)
        {
            int r = (int)(c1.R + (c2.R - c1.R) * t);
            int g = (int)(c1.G + (c2.G - c1.G) * t);
            int b = (int)(c1.B + (c2.B - c1.B) * t);
            // alfa medio per non rompere visibilità dei disegni del prezzo
            return Color.FromArgb(200, Clamp(r), Clamp(g), Clamp(b));
        }

        private int Clamp(int v) => Math.Max(0, Math.Min(255, v));
    }
}
