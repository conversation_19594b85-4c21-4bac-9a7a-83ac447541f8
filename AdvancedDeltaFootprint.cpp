#include "sierrachart.h"

SCDLLName("Advanced Delta Footprint Study")

/*==========================================================================*/
SCSFExport scsf_AdvancedDeltaFootprint(SCStudyInterfaceRef sc)
{
    // Input references
    SCInputRef SmoothingPeriod = sc.Input[0];
    SCInputRef SmoothingType = sc.Input[1];
    SCInputRef DisplayMode = sc.Input[2];
    SCInputRef MinVolumeFilter = sc.Input[3];
    SCInputRef FontSize = sc.Input[4];
    SCInputRef ShowImbalanceOnly = sc.Input[5];
    SCInputRef ImbalanceRatio = sc.Input[6];
    SCInputRef PositiveDeltaColor = sc.Input[7];
    SCInputRef NegativeDeltaColor = sc.Input[8];
    SCInputRef NeutralColor = sc.Input[9];
    SCInputRef HighlightPOCDelta = sc.Input[10];
    SCInputRef POCHighlightColor = sc.Input[11];

    if (sc.SetDefaults)
    {
        sc.GraphName = "Advanced Delta Footprint";
        sc.StudyDescription = "Advanced footprint study showing smoothed delta values at each price level with volume filtering, imbalance detection, and POC highlighting.";
        
        sc.GraphRegion = 0; // Main price graph
        sc.AutoLoop = 0; // Manual loop control
        sc.CalculationPrecedence = LOW_PREC_LEVEL;
        sc.FreeDLL = 0;
        
        // Input parameters
        SmoothingPeriod.Name = "Smoothing Period";
        SmoothingPeriod.SetInt(3);
        SmoothingPeriod.SetIntLimits(1, 20);
        SmoothingPeriod.SetDescription("Bars to smooth delta values across");
        
        SmoothingType.Name = "Smoothing Type";
        SmoothingType.SetMovAvgType(MOVAVGTYPE_EXPONENTIAL);
        
        DisplayMode.Name = "Display Mode";
        DisplayMode.SetCustomInputStrings("Delta Values;Imbalance %;Both");
        DisplayMode.SetCustomInputIndex(0);
        DisplayMode.SetDescription("What to display at each price level");
        
        MinVolumeFilter.Name = "Minimum Volume Filter";
        MinVolumeFilter.SetInt(10);
        MinVolumeFilter.SetIntLimits(0, 1000);
        MinVolumeFilter.SetDescription("Minimum total volume to show price level");
        
        FontSize.Name = "Font Size";
        FontSize.SetInt(8);
        FontSize.SetIntLimits(6, 16);
        
        ShowImbalanceOnly.Name = "Show Imbalances Only";
        ShowImbalanceOnly.SetYesNo(0);
        ShowImbalanceOnly.SetDescription("Only show levels with significant imbalances");
        
        ImbalanceRatio.Name = "Imbalance Ratio Threshold";
        ImbalanceRatio.SetFloat(2.0f);
        ImbalanceRatio.SetFloatLimits(1.1f, 10.0f);
        ImbalanceRatio.SetDescription("Minimum ratio for imbalance detection");
        
        PositiveDeltaColor.Name = "Positive Delta Color";
        PositiveDeltaColor.SetColor(RGB(0, 255, 0));
        
        NegativeDeltaColor.Name = "Negative Delta Color";
        NegativeDeltaColor.SetColor(RGB(255, 0, 0));
        
        NeutralColor.Name = "Neutral Color";
        NeutralColor.SetColor(RGB(200, 200, 200));
        
        HighlightPOCDelta.Name = "Highlight POC Delta";
        HighlightPOCDelta.SetYesNo(1);
        HighlightPOCDelta.SetDescription("Highlight delta at Point of Control");
        
        POCHighlightColor.Name = "POC Highlight Color";
        POCHighlightColor.SetColor(RGB(255, 255, 0)); // Yellow
        
        return;
    }

    // Only process completed bars
    if (sc.GetBarHasClosedStatus(sc.Index) == BHCS_BAR_HAS_NOT_CLOSED)
        return;

    if (sc.VolumeAtPriceForBars == NULL)
    {
        sc.AddMessageToLog("Volume at Price data required. Enable in Chart Settings.", 1);
        return;
    }

    int BarIndex = sc.Index;
    int LowTick = sc.PriceValueToTicks(sc.Low[BarIndex]);
    int HighTick = sc.PriceValueToTicks(sc.High[BarIndex]);
    
    if (LowTick == HighTick)
        return;
    
    // Find POC (Point of Control) for this bar
    int POCTick = LowTick;
    unsigned int MaxVolume = 0;
    
    for (int PriceTick = LowTick; PriceTick <= HighTick; PriceTick++)
    {
        unsigned int TotalVol = sc.VolumeAtPriceForBars->GetVolumeAtPrice(BarIndex, PriceTick);
        if (TotalVol > MaxVolume)
        {
            MaxVolume = TotalVol;
            POCTick = PriceTick;
        }
    }
    
    int SmoothPeriod = SmoothingPeriod.GetInt();
    int DisplayModeValue = DisplayMode.GetIndex();
    int MinVolume = MinVolumeFilter.GetInt();
    bool ImbalanceOnly = ShowImbalanceOnly.GetYesNo();
    float ImbalanceThreshold = ImbalanceRatio.GetFloat();
    bool HighlightPOC = HighlightPOCDelta.GetYesNo();
    
    int LineNumber = BarIndex * 10000;
    
    // Process each price level
    for (int PriceTick = LowTick; PriceTick <= HighTick; PriceTick++)
    {
        unsigned int BidVol = sc.VolumeAtPriceForBars->GetBidVolumeAtPrice(BarIndex, PriceTick);
        unsigned int AskVol = sc.VolumeAtPriceForBars->GetAskVolumeAtPrice(BarIndex, PriceTick);
        unsigned int TotalVol = BidVol + AskVol;
        
        // Apply volume filter
        if (TotalVol < MinVolume)
            continue;
            
        // Calculate raw delta
        int RawDelta = (int)AskVol - (int)BidVol;
        
        // Calculate smoothed delta across multiple bars
        float SmoothedDelta = (float)RawDelta;
        if (SmoothPeriod > 1 && BarIndex >= SmoothPeriod - 1)
        {
            float SumDelta = 0.0f;
            float SumWeight = 0.0f;
            
            for (int i = 0; i < SmoothPeriod; i++)
            {
                int LookbackIndex = BarIndex - i;
                if (LookbackIndex >= 0)
                {
                    unsigned int PrevBidVol = sc.VolumeAtPriceForBars->GetBidVolumeAtPrice(LookbackIndex, PriceTick);
                    unsigned int PrevAskVol = sc.VolumeAtPriceForBars->GetAskVolumeAtPrice(LookbackIndex, PriceTick);
                    unsigned int PrevTotalVol = PrevBidVol + PrevAskVol;
                    
                    if (PrevTotalVol > 0)
                    {
                        float PrevDelta = (float)((int)PrevAskVol - (int)PrevBidVol);
                        float Weight = (float)(SmoothPeriod - i); // More weight to recent bars
                        SumDelta += PrevDelta * Weight;
                        SumWeight += Weight;
                    }
                }
            }
            
            if (SumWeight > 0)
                SmoothedDelta = SumDelta / SumWeight;
        }
        
        // Check for imbalance if filtering enabled
        if (ImbalanceOnly)
        {
            if (BidVol == 0 && AskVol == 0)
                continue;
                
            float Ratio = 1.0f;
            if (BidVol > 0 && AskVol > 0)
            {
                Ratio = (BidVol > AskVol) ? (float)BidVol / AskVol : (float)AskVol / BidVol;
            }
            else if (BidVol == 0 || AskVol == 0)
            {
                Ratio = ImbalanceThreshold; // Consider one-sided volume as imbalance
            }
            
            if (Ratio < ImbalanceThreshold)
                continue;
        }
        
        // Determine what to display
        SCString DisplayText;
        if (DisplayModeValue == 0) // Delta Values
        {
            if (abs((int)SmoothedDelta) >= 1000)
                DisplayText.Format("%.1fk", SmoothedDelta / 1000.0f);
            else
                DisplayText.Format("%.0f", SmoothedDelta);
        }
        else if (DisplayModeValue == 1) // Imbalance %
        {
            if (BidVol > 0 && AskVol > 0)
            {
                float ImbalancePercent = (SmoothedDelta / (float)TotalVol) * 100.0f;
                DisplayText.Format("%.0f%%", ImbalancePercent);
            }
            else if (AskVol > 0)
                DisplayText = "100%";
            else if (BidVol > 0)
                DisplayText = "-100%";
        }
        else // Both
        {
            if (abs((int)SmoothedDelta) >= 1000)
                DisplayText.Format("%.1fk", SmoothedDelta / 1000.0f);
            else
                DisplayText.Format("%.0f", SmoothedDelta);
                
            if (BidVol > 0 && AskVol > 0)
            {
                float ImbalancePercent = (SmoothedDelta / (float)TotalVol) * 100.0f;
                SCString PercentText;
                PercentText.Format(" (%.0f%%)", ImbalancePercent);
                DisplayText += PercentText;
            }
        }
        
        // Determine color
        COLORREF TextColor = NeutralColor.GetColor();
        if (SmoothedDelta > 0)
            TextColor = PositiveDeltaColor.GetColor();
        else if (SmoothedDelta < 0)
            TextColor = NegativeDeltaColor.GetColor();
            
        // Highlight POC if enabled
        if (HighlightPOC && PriceTick == POCTick)
            TextColor = POCHighlightColor.GetColor();
        
        // Create text drawing
        float PriceLevel = sc.TicksToPriceValue(PriceTick);
        
        s_UseTool Tool;
        Tool.Clear();
        Tool.ChartNumber = sc.ChartNumber;
        Tool.DrawingType = DRAWING_TEXT;
        Tool.LineNumber = LineNumber + (PriceTick - LowTick);
        Tool.BeginIndex = BarIndex;
        Tool.BeginValue = PriceLevel;
        Tool.Text = DisplayText;
        Tool.Color = TextColor;
        Tool.FontBold = (HighlightPOC && PriceTick == POCTick) ? 1 : 0;
        Tool.FontSize = FontSize.GetInt();
        Tool.TextAlignment = DT_CENTER | DT_VCENTER;
        Tool.AddMethod = UTAM_ADD_OR_ADJUST;
        Tool.ReverseTextColor = 0;
        Tool.TransparencyLevel = 0;
        
        sc.UseTool(Tool);
    }
}
