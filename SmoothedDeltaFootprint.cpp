#include "sierrachart.h"

SCDLLName("Smoothed Delta Footprint Study")

/*==========================================================================*/
SCSFExport scsf_SmoothedDeltaFootprint(SCStudyInterfaceRef sc)
{
    // Input references
    SCInputRef SmoothingPeriod = sc.Input[0];
    SCInputRef SmoothingType = sc.Input[1];
    SCInputRef DisplayMode = sc.Input[2];
    SCInputRef PositiveDeltaColor = sc.Input[3];
    SCInputRef NegativeDeltaColor = sc.Input[4];
    SCInputRef NeutralColor = sc.Input[5];
    SCInputRef ShowRawDelta = sc.Input[6];
    SCInputRef DeltaThreshold = sc.Input[7];

    // Subgraph references
    SCSubgraphRef SmoothedDelta = sc.Subgraph[0];
    SCSubgraphRef RawDelta = sc.Subgraph[1];
    SCSubgraphRef DeltaHeatmap = sc.Subgraph[2];  // For gradient coloring
    SCSubgraphRef PositiveSignals = sc.Subgraph[3];
    SCSubgraphRef NegativeSignals = sc.Subgraph[4];
    SCSubgraphRef ZeroLine = sc.Subgraph[5];

    // Internal arrays for calculations
    SCFloatArrayRef DeltaArray = sc.Subgraph[0].Arrays[0];
    SCFloatArrayRef AbsDeltaArray = sc.Subgraph[0].Arrays[1];

    if (sc.SetDefaults)
    {
        // Set the configuration and defaults
        sc.GraphName = "Smoothed Delta Footprint";
        sc.StudyDescription = "Displays smoothed delta values with footprint-style visualization. Shows the difference between aggressive buying and selling pressure with various smoothing options.";
        
        sc.GraphRegion = 1; // Separate window
        sc.AutoLoop = 1;
        sc.CalculationPrecedence = LOW_PREC_LEVEL;
        
        // Input parameters
        SmoothingPeriod.Name = "Smoothing Period";
        SmoothingPeriod.SetInt(14);
        SmoothingPeriod.SetIntLimits(1, 200);
        SmoothingPeriod.SetDescription("Number of bars to use for smoothing calculation");
        
        SmoothingType.Name = "Smoothing Type";
        SmoothingType.SetMovAvgType(MOVAVGTYPE_EXPONENTIAL);
        SmoothingType.SetDescription("Type of moving average for smoothing");
        
        DisplayMode.Name = "Display Mode";
        DisplayMode.SetCustomInputStrings("Line;Histogram;Both");
        DisplayMode.SetCustomInputIndex(2);
        DisplayMode.SetDescription("How to display the smoothed delta");
        
        PositiveDeltaColor.Name = "Positive Delta Color";
        PositiveDeltaColor.SetColor(RGB(0, 255, 0)); // Green
        
        NegativeDeltaColor.Name = "Negative Delta Color";
        NegativeDeltaColor.SetColor(RGB(255, 0, 0)); // Red
        
        NeutralColor.Name = "Neutral Color";
        NeutralColor.SetColor(RGB(128, 128, 128)); // Gray
        
        ShowRawDelta.Name = "Show Raw Delta";
        ShowRawDelta.SetYesNo(0);
        ShowRawDelta.SetDescription("Display raw delta values alongside smoothed values");
        
        DeltaThreshold.Name = "Signal Threshold";
        DeltaThreshold.SetFloat(0.0f);
        DeltaThreshold.SetFloatLimits(0.0f, 10000.0f);
        DeltaThreshold.SetDescription("Minimum delta value to generate signals (0 = disabled)");

        // Configure subgraphs
        SmoothedDelta.Name = "Smoothed Delta";
        SmoothedDelta.DrawStyle = DRAWSTYLE_LINE;
        SmoothedDelta.PrimaryColor = RGB(0, 162, 232);
        SmoothedDelta.LineWidth = 2;
        SmoothedDelta.DrawZeros = false;
        
        RawDelta.Name = "Raw Delta";
        RawDelta.DrawStyle = DRAWSTYLE_LINE;
        RawDelta.PrimaryColor = RGB(255, 165, 0);
        RawDelta.LineWidth = 1;
        RawDelta.DrawZeros = false;
        
        DeltaHeatmap.Name = "Delta Heatmap";
        DeltaHeatmap.DrawStyle = DRAWSTYLE_COLOR_BAR;
        DeltaHeatmap.PrimaryColor = RGB(128, 128, 128);
        DeltaHeatmap.DrawZeros = false;
        
        PositiveSignals.Name = "Positive Signals";
        PositiveSignals.DrawStyle = DRAWSTYLE_TRIANGLE_UP;
        PositiveSignals.PrimaryColor = RGB(0, 255, 0);
        PositiveSignals.LineWidth = 3;
        PositiveSignals.DrawZeros = false;
        
        NegativeSignals.Name = "Negative Signals";
        NegativeSignals.DrawStyle = DRAWSTYLE_TRIANGLE_DOWN;
        NegativeSignals.PrimaryColor = RGB(255, 0, 0);
        NegativeSignals.LineWidth = 3;
        NegativeSignals.DrawZeros = false;
        
        ZeroLine.Name = "Zero Line";
        ZeroLine.DrawStyle = DRAWSTYLE_LINE;
        ZeroLine.PrimaryColor = RGB(128, 128, 128);
        ZeroLine.LineWidth = 1;
        ZeroLine.DrawZeros = true;
        
        return;
    }

    // Calculate raw delta (Ask Volume - Bid Volume)
    float RawDeltaValue = sc.AskVolume[sc.Index] - sc.BidVolume[sc.Index];
    DeltaArray[sc.Index] = RawDeltaValue;
    AbsDeltaArray[sc.Index] = fabs(RawDeltaValue);
    
    // Set raw delta if enabled
    if (ShowRawDelta.GetYesNo())
    {
        RawDelta[sc.Index] = RawDeltaValue;
    }
    
    // Calculate smoothed delta using selected moving average type
    int Period = SmoothingPeriod.GetInt();
    unsigned int MAType = SmoothingType.GetMovAvgType();
    
    sc.MovingAverage(DeltaArray, SmoothedDelta, MAType, Period);
    
    // Set display based on mode
    int DisplayModeValue = DisplayMode.GetIndex();
    float SmoothedValue = SmoothedDelta[sc.Index];
    
    if (DisplayModeValue == 0 || DisplayModeValue == 2) // Line or Both
    {
        SmoothedDelta.DrawStyle = DRAWSTYLE_LINE;
    }
    
    if (DisplayModeValue == 1 || DisplayModeValue == 2) // Histogram or Both
    {
        DeltaHeatmap[sc.Index] = SmoothedValue;
        
        // Set color based on delta value
        if (SmoothedValue > 0)
        {
            DeltaHeatmap.DataColor[sc.Index] = PositiveDeltaColor.GetColor();
        }
        else if (SmoothedValue < 0)
        {
            DeltaHeatmap.DataColor[sc.Index] = NegativeDeltaColor.GetColor();
        }
        else
        {
            DeltaHeatmap.DataColor[sc.Index] = NeutralColor.GetColor();
        }
    }
    
    // Generate signals if threshold is set
    float Threshold = DeltaThreshold.GetFloat();
    if (Threshold > 0.0f)
    {
        if (SmoothedValue >= Threshold)
        {
            PositiveSignals[sc.Index] = SmoothedValue;
        }
        else if (SmoothedValue <= -Threshold)
        {
            NegativeSignals[sc.Index] = SmoothedValue;
        }
    }
    
    // Always draw zero line
    ZeroLine[sc.Index] = 0.0f;
}
