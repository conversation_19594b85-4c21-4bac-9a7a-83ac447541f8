#include "sierrachart.h"

SCDLLName("Advanced Smoothed Delta Footprint Study")

/*==========================================================================*/
SCSFExport scsf_AdvancedSmoothedDeltaFootprint(SCStudyInterfaceRef sc)
{
    // Input references
    SCInputRef SmoothingPeriod = sc.Input[0];
    SCInputRef SmoothingType = sc.Input[1];
    SCInputRef NormalizationPeriod = sc.Input[2];
    SCInputRef UseVolumeWeighting = sc.Input[3];
    SCInputRef ShowCumulativeDelta = sc.Input[4];
    SCInputRef ResetCumulative = sc.Input[5];
    SCInputRef DeltaStrengthFilter = sc.Input[6];
    SCInputRef ShowDivergenceSignals = sc.Input[7];
    SCInputRef PositiveDeltaColor = sc.Input[8];
    SCInputRef NegativeDeltaColor = sc.Input[9];
    SCInputRef NeutralColor = sc.Input[10];

    // Subgraph references
    SCSubgraphRef SmoothedDelta = sc.Subgraph[0];
    SCSubgraphRef NormalizedDelta = sc.Subgraph[1];
    SCSubgraphRef CumulativeDelta = sc.Subgraph[2];
    SCSubgraphRef DeltaStrength = sc.Subgraph[3];
    SCSubgraphRef BullishDivergence = sc.Subgraph[4];
    SCSubgraphRef BearishDivergence = sc.Subgraph[5];
    SCSubgraphRef ZeroLine = sc.Subgraph[6];
    SCSubgraphRef UpperThreshold = sc.Subgraph[7];
    SCSubgraphRef LowerThreshold = sc.Subgraph[8];

    // Internal arrays
    SCFloatArrayRef DeltaArray = sc.Subgraph[0].Arrays[0];
    SCFloatArrayRef VolumeArray = sc.Subgraph[0].Arrays[1];
    SCFloatArrayRef AvgVolumeArray = sc.Subgraph[0].Arrays[2];
    SCFloatArrayRef DeltaRangeArray = sc.Subgraph[0].Arrays[3];

    if (sc.SetDefaults)
    {
        sc.GraphName = "Advanced Smoothed Delta Footprint";
        sc.StudyDescription = "Advanced delta footprint analysis with normalization, volume weighting, cumulative delta, and divergence detection.";
        
        sc.GraphRegion = 1;
        sc.AutoLoop = 1;
        sc.CalculationPrecedence = LOW_PREC_LEVEL;
        
        // Input parameters
        SmoothingPeriod.Name = "Smoothing Period";
        SmoothingPeriod.SetInt(14);
        SmoothingPeriod.SetIntLimits(2, 200);
        
        SmoothingType.Name = "Smoothing Type";
        SmoothingType.SetMovAvgType(MOVAVGTYPE_EXPONENTIAL);
        
        NormalizationPeriod.Name = "Normalization Period";
        NormalizationPeriod.SetInt(50);
        NormalizationPeriod.SetIntLimits(10, 500);
        NormalizationPeriod.SetDescription("Period for normalizing delta values");
        
        UseVolumeWeighting.Name = "Use Volume Weighting";
        UseVolumeWeighting.SetYesNo(1);
        UseVolumeWeighting.SetDescription("Weight delta by volume for more accurate signals");
        
        ShowCumulativeDelta.Name = "Show Cumulative Delta";
        ShowCumulativeDelta.SetYesNo(0);
        ShowCumulativeDelta.SetDescription("Display cumulative delta line");
        
        ResetCumulative.Name = "Reset Cumulative Daily";
        ResetCumulative.SetYesNo(1);
        ResetCumulative.SetDescription("Reset cumulative delta at start of each day");
        
        DeltaStrengthFilter.Name = "Delta Strength Filter";
        DeltaStrengthFilter.SetFloat(1.5f);
        DeltaStrengthFilter.SetFloatLimits(0.5f, 5.0f);
        DeltaStrengthFilter.SetDescription("Minimum strength multiplier for signals");
        
        ShowDivergenceSignals.Name = "Show Divergence Signals";
        ShowDivergenceSignals.SetYesNo(1);
        ShowDivergenceSignals.SetDescription("Detect and display price-delta divergences");
        
        PositiveDeltaColor.Name = "Positive Delta Color";
        PositiveDeltaColor.SetColor(RGB(0, 255, 0));
        
        NegativeDeltaColor.Name = "Negative Delta Color";
        NegativeDeltaColor.SetColor(RGB(255, 0, 0));
        
        NeutralColor.Name = "Neutral Color";
        NeutralColor.SetColor(RGB(128, 128, 128));

        // Configure subgraphs
        SmoothedDelta.Name = "Smoothed Delta";
        SmoothedDelta.DrawStyle = DRAWSTYLE_LINE;
        SmoothedDelta.PrimaryColor = RGB(0, 162, 232);
        SmoothedDelta.LineWidth = 2;
        
        NormalizedDelta.Name = "Normalized Delta";
        NormalizedDelta.DrawStyle = DRAWSTYLE_COLOR_BAR;
        NormalizedDelta.PrimaryColor = RGB(128, 128, 128);
        NormalizedDelta.LineWidth = 3;
        
        CumulativeDelta.Name = "Cumulative Delta";
        CumulativeDelta.DrawStyle = DRAWSTYLE_LINE;
        CumulativeDelta.PrimaryColor = RGB(255, 165, 0);
        CumulativeDelta.LineWidth = 1;
        
        DeltaStrength.Name = "Delta Strength";
        DeltaStrength.DrawStyle = DRAWSTYLE_IGNORE;
        
        BullishDivergence.Name = "Bullish Divergence";
        BullishDivergence.DrawStyle = DRAWSTYLE_TRIANGLE_UP;
        BullishDivergence.PrimaryColor = RGB(0, 255, 0);
        BullishDivergence.LineWidth = 4;
        BullishDivergence.DrawZeros = false;
        
        BearishDivergence.Name = "Bearish Divergence";
        BearishDivergence.DrawStyle = DRAWSTYLE_TRIANGLE_DOWN;
        BearishDivergence.PrimaryColor = RGB(255, 0, 0);
        BearishDivergence.LineWidth = 4;
        BearishDivergence.DrawZeros = false;
        
        ZeroLine.Name = "Zero Line";
        ZeroLine.DrawStyle = DRAWSTYLE_LINE;
        ZeroLine.PrimaryColor = RGB(128, 128, 128);
        ZeroLine.LineWidth = 1;
        ZeroLine.DrawZeros = true;
        
        UpperThreshold.Name = "Upper Threshold";
        UpperThreshold.DrawStyle = DRAWSTYLE_DASH;
        UpperThreshold.PrimaryColor = RGB(0, 255, 0);
        UpperThreshold.LineWidth = 1;
        
        LowerThreshold.Name = "Lower Threshold";
        LowerThreshold.DrawStyle = DRAWSTYLE_DASH;
        LowerThreshold.PrimaryColor = RGB(255, 0, 0);
        LowerThreshold.LineWidth = 1;
        
        return;
    }

    // Calculate raw delta and volume
    float RawDelta = sc.AskVolume[sc.Index] - sc.BidVolume[sc.Index];
    float TotalVolume = sc.AskVolume[sc.Index] + sc.BidVolume[sc.Index];
    
    DeltaArray[sc.Index] = RawDelta;
    VolumeArray[sc.Index] = TotalVolume;
    
    int SmoothPeriod = SmoothingPeriod.GetInt();
    int NormPeriod = NormalizationPeriod.GetInt();
    unsigned int MAType = SmoothingType.GetMovAvgType();
    
    // Calculate average volume for normalization
    sc.MovingAverage(VolumeArray, AvgVolumeArray, MOVAVGTYPE_SIMPLE, NormPeriod);
    
    // Apply volume weighting if enabled
    if (UseVolumeWeighting.GetYesNo() && AvgVolumeArray[sc.Index] > 0)
    {
        float VolumeWeight = TotalVolume / AvgVolumeArray[sc.Index];
        DeltaArray[sc.Index] = RawDelta * VolumeWeight;
    }
    
    // Calculate smoothed delta
    sc.MovingAverage(DeltaArray, SmoothedDelta, MAType, SmoothPeriod);
    
    // Calculate delta range for normalization
    sc.HighestHigh(DeltaArray, DeltaRangeArray, NormPeriod);
    float DeltaHigh = DeltaRangeArray[sc.Index];
    sc.LowestLow(DeltaArray, DeltaRangeArray, NormPeriod);
    float DeltaLow = DeltaRangeArray[sc.Index];
    float DeltaRange = DeltaHigh - DeltaLow;
    
    // Normalize delta (-1 to +1 range)
    if (DeltaRange > 0)
    {
        NormalizedDelta[sc.Index] = (SmoothedDelta[sc.Index] - DeltaLow) / DeltaRange * 2.0f - 1.0f;
    }
    else
    {
        NormalizedDelta[sc.Index] = 0.0f;
    }
    
    // Set color for normalized delta bar
    float NormValue = NormalizedDelta[sc.Index];
    if (NormValue > 0.1f)
    {
        NormalizedDelta.DataColor[sc.Index] = PositiveDeltaColor.GetColor();
    }
    else if (NormValue < -0.1f)
    {
        NormalizedDelta.DataColor[sc.Index] = NegativeDeltaColor.GetColor();
    }
    else
    {
        NormalizedDelta.DataColor[sc.Index] = NeutralColor.GetColor();
    }
    
    // Calculate cumulative delta if enabled
    if (ShowCumulativeDelta.GetYesNo())
    {
        if (sc.Index == 0 || (ResetCumulative.GetYesNo() && sc.BaseDateTimeIn[sc.Index].GetDate() != sc.BaseDateTimeIn[sc.Index-1].GetDate()))
        {
            CumulativeDelta[sc.Index] = SmoothedDelta[sc.Index];
        }
        else
        {
            CumulativeDelta[sc.Index] = CumulativeDelta[sc.Index-1] + SmoothedDelta[sc.Index];
        }
    }
    
    // Calculate delta strength
    float StrengthFilter = DeltaStrengthFilter.GetFloat();
    if (AvgVolumeArray[sc.Index] > 0)
    {
        DeltaStrength[sc.Index] = fabs(SmoothedDelta[sc.Index]) / AvgVolumeArray[sc.Index];
    }
    
    // Divergence detection
    if (ShowDivergenceSignals.GetYesNo() && sc.Index >= 20)
    {
        // Look for price highs/lows vs delta highs/lows
        int LookbackPeriod = 10;

        // Find recent price high/low
        float RecentPriceHigh = sc.GetHighest(sc.High, LookbackPeriod);
        float RecentPriceLow = sc.GetLowest(sc.Low, LookbackPeriod);
        float CurrentPrice = sc.Close[sc.Index];

        // Find recent delta high/low
        float RecentDeltaHigh = sc.GetHighest(SmoothedDelta, LookbackPeriod);
        float RecentDeltaLow = sc.GetLowest(SmoothedDelta, LookbackPeriod);
        float CurrentDelta = SmoothedDelta[sc.Index];

        // Bullish divergence: Price makes lower low, but delta makes higher low
        if (CurrentPrice <= RecentPriceLow * 1.001f && CurrentDelta >= RecentDeltaLow * 1.1f &&
            DeltaStrength[sc.Index] >= StrengthFilter)
        {
            BullishDivergence[sc.Index] = SmoothedDelta[sc.Index];
        }

        // Bearish divergence: Price makes higher high, but delta makes lower high
        if (CurrentPrice >= RecentPriceHigh * 0.999f && CurrentDelta <= RecentDeltaHigh * 0.9f &&
            DeltaStrength[sc.Index] >= StrengthFilter)
        {
            BearishDivergence[sc.Index] = SmoothedDelta[sc.Index];
        }
    }

    // Set threshold lines
    float ThresholdValue = StrengthFilter * 0.5f;
    UpperThreshold[sc.Index] = ThresholdValue;
    LowerThreshold[sc.Index] = -ThresholdValue;
    ZeroLine[sc.Index] = 0.0f;
}
